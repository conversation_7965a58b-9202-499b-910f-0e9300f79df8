{"PatternReplacement": {"IsEnabled": false, "ReplacementRules": [{"IsEnabled": true, "RuleName": "4536", "MatchType": 0, "SourcePattern": "345734", "TargetPattern": "34636", "CaseSensitive": true, "IncludeExtension": true, "CreatedTime": "2025-06-04T21:18:03.2219344+08:00"}]}, "ConflictHandling": {"ConflictHandlingType": 0, "AutoRenameFormat": "{filename}_{index}", "OverwriteExisting": false, "SkipConflicts": false}}